#!/usr/bin/env python3
"""
Generate July 2025 Employee-Based Report with PROPER CATEGORIZATION
- New employees: 4+ items added in PERSONAL DETAILS
- Removed employees: 4+ items removed in PERSONAL DETAILS  
- Transfers: Department changes
- Promotions: Job title changes
- Individual narratives: Only for employees NOT in appendix categories
"""

import sys
import os
import sqlite3
from datetime import datetime
from docx import Document
from docx.shared import Inches
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.table import WD_TABLE_ALIGNMENT

# SPECIFIC SECTIONS AND ITEMS TO INCLUDE (EXACT MATCH REQUIRED)
ALLOWED_SECTIONS_AND_ITEMS = {
    'DEDUCTIONS': [
        'CODE Z', 'COP HQ HUMAN RESOURCE W', 'COPHSWA WELFARE', 'FINANCE STAFF WELFARE',
        'HUMAN RESOURCE WELFARE', 'INTERNAL AUDIT WELFARE', 'LEAVE ALLOWANCE MISSIONS',
        'LEAVE PENSIONS OFFICE', 'MINISTERS PENSION', 'MINISTERS-PENSION',
        'MISSIONS STAFF WELFARE', 'PENT TV WELFARE', 'PROVIDENT FUND',
        'RENT ELEMENT - STAFF', 'RETAINABLE ALLOWANCE-HQ', 'RETAINABLE DED-INT. MISSIO',
        'SCHOLARSHIP FUND', 'SSF EMPLOYEE', 'TITHES', 'TRANSPORT ELEMENT',
        'TRANSPORT ELEMENT - MISSI', 'TRANSPORT ELEMENT - PENSI', 'TRANSPORT STAFF WELFARE',
        'WELFARE FUND', 'WOMENS WELFARE DEDUCTIO'
    ],
    'EARNINGS': [
        '1ST FUEL ELEMENT', '1ST FUEL ELEMENT - MIS', '1ST FUEL ELEMENT - PEN',
        '2ND FUEL ELEMENT', '2ND FUEL ELEMENT - MIS', 'ALL INCLUSIVE ALL. MISS',
        'ALL INCLUSIVE PENT TV', 'ALLINCLUSIVE ALLOWAN', 'EXEC. DRIVERS ALL. MIS',
        'EXECUTIVE DRIVERS ALL', 'HEAD OF DEPT ALLOWAN', 'HONORARIUM',
        'HOUSING ALLOWANCE  D', 'PROFESSIONAL ALLOWA', 'RENT ALLOWANCE',
        'RENT ELEMENT', 'RESPONSIBILITY ALLOW', 'RESPONSIBILITY ALLOW.',
        'RESPONSIBILITY HEADS', 'RISK ALLOWANCE', 'RISK ALLOWANCE  STAFF',
        'RISKSECURITY MEN', 'SECONDMENT ALL.  CAT1', 'SECONDMENT ALL.  PRE',
        'SECURITY GUARD ALLO', 'SUBSISTENCE ~ STAFF', 'SUBSISTENCEAREA HEA',
        'SUBSISTENCEMINISTERS', 'SUSTAINABILITY ALLOWA', 'VEHICLE MAINT ALL  STA',
        'VEHICLE MAINTENANCE'
    ],
    'EMPLOYEE BANK DETAILS': [
        'ACCOUNT NO.', 'BANK', 'BRANCH'
    ],
    'PERSONAL DETAILS': [
        'EMPLOYEE NAME', 'EMPLOYEE NO.', 'JOB TITLE'
    ]
}

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    return None

def is_allowed_section_item(section_name, item_label):
    """Check if section and item combination is in allowed list"""
    section_upper = section_name.upper()
    item_upper = item_label.upper()
    
    if section_upper in ALLOWED_SECTIONS_AND_ITEMS:
        return item_upper in ALLOWED_SECTIONS_AND_ITEMS[section_upper]
    return False

def load_and_categorize_july_2025_data():
    """Load July 2025 data and properly categorize employees"""
    print("📊 LOADING AND CATEGORIZING JULY 2025 DATA")
    print("=" * 50)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return None
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get current session
        cursor.execute('SELECT session_id FROM current_session WHERE id = 1')
        current_session_result = cursor.fetchone()
        
        if not current_session_result:
            print("❌ No current session found")
            return None
        
        current_session_id = current_session_result[0]
        print(f"✅ Using session: {current_session_id}")
        
        # Load ALL comparison results first
        cursor.execute('''
            SELECT employee_id, employee_name, section_name, item_label, 
                   previous_value, current_value, change_type, priority,
                   numeric_difference, percentage_change
            FROM comparison_results 
            WHERE change_type != 'NO_CHANGE'
            AND session_id = ?
            ORDER BY employee_name, section_name, item_label
        ''', (current_session_id,))
        
        all_results = cursor.fetchall()
        
        # Filter for ONLY specified sections and items
        specific_results = []
        for result in all_results:
            section_name = result[2]
            item_label = result[3]
            
            if is_allowed_section_item(section_name, item_label):
                specific_results.append(result)
        
        conn.close()
        
        print(f"✅ Total comparison results: {len(all_results)}")
        print(f"✅ Filtered to specific sections: {len(specific_results)}")
        
        # Now categorize employees properly
        return categorize_employees_properly(specific_results)
        
    except Exception as e:
        print(f"❌ Error loading data: {e}")
        import traceback
        print(f"📋 Traceback: {traceback.format_exc()}")
        return None

def categorize_employees_properly(specific_results):
    """Properly categorize employees based on business rules"""
    print("\n🧠 CATEGORIZING EMPLOYEES PROPERLY")
    print("=" * 50)
    
    # Group all changes by employee
    employee_changes = {}
    for result in specific_results:
        emp_id = result[0]
        if emp_id not in employee_changes:
            employee_changes[emp_id] = {
                'employee_id': emp_id,
                'employee_name': result[1],
                'changes': []
            }
        
        change = {
            'section_name': result[2],
            'item_name': result[3],
            'previous_value': result[4] or '',
            'current_value': result[5] or '',
            'change_type': result[6],
            'priority': result[7] if len(result) > 7 else 'HIGH'
        }
        employee_changes[emp_id]['changes'].append(change)
    
    # Categorize employees
    new_employees = []
    removed_employees = []
    promotions = []
    transfers = []
    regular_employees = {}
    
    for emp_id, emp_data in employee_changes.items():
        # Count PERSONAL DETAILS changes
        personal_added = 0
        personal_removed = 0
        has_job_title_change = False
        has_department_change = False
        
        for change in emp_data['changes']:
            if change['section_name'].upper() == 'PERSONAL DETAILS':
                if change['change_type'] in ['NEW', 'ADDED']:
                    personal_added += 1
                elif change['change_type'] in ['REMOVED', 'DELETED']:
                    personal_removed += 1
                elif change['item_name'].upper() == 'JOB TITLE' and change['change_type'] == 'CHANGED':
                    has_job_title_change = True
                elif change['item_name'].upper() in ['DEPARTMENT', 'SECTION'] and change['change_type'] == 'CHANGED':
                    has_department_change = True
        
        # Categorize based on rules
        if personal_removed >= 4:
            removed_employees.append({
                'employee_id': emp_id,
                'employee_name': emp_data['employee_name'],
                'removed_items': personal_removed
            })
        elif personal_added >= 4:
            new_employees.append({
                'employee_id': emp_id,
                'employee_name': emp_data['employee_name'],
                'added_items': personal_added
            })
        elif has_job_title_change:
            # Find the job title change
            for change in emp_data['changes']:
                if change['item_name'].upper() == 'JOB TITLE':
                    promotions.append({
                        'employee_id': emp_id,
                        'employee_name': emp_data['employee_name'],
                        'previous_title': change['previous_value'],
                        'current_title': change['current_value']
                    })
                    break
        elif has_department_change:
            # Find the department change
            for change in emp_data['changes']:
                if change['item_name'].upper() in ['DEPARTMENT', 'SECTION']:
                    transfers.append({
                        'employee_id': emp_id,
                        'employee_name': emp_data['employee_name'],
                        'previous_department': change['previous_value'],
                        'current_department': change['current_value']
                    })
                    break
        else:
            # Regular employee with individual changes to report
            # Filter out PERSONAL DETAILS changes for regular employees
            filtered_changes = []
            for change in emp_data['changes']:
                if change['section_name'].upper() != 'PERSONAL DETAILS':
                    filtered_changes.append(change)
            
            if filtered_changes:  # Only include if they have non-personal changes
                regular_employees[emp_id] = {
                    'employee_id': emp_id,
                    'employee_name': emp_data['employee_name'],
                    'changes': filtered_changes
                }
    
    print(f"✅ NEW EMPLOYEES (4+ personal items added): {len(new_employees)}")
    print(f"✅ REMOVED EMPLOYEES (4+ personal items removed): {len(removed_employees)}")
    print(f"✅ PROMOTIONS (job title changes): {len(promotions)}")
    print(f"✅ TRANSFERS (department changes): {len(transfers)}")
    print(f"✅ REGULAR EMPLOYEES (individual changes): {len(regular_employees)}")
    
    return {
        'new_employees': new_employees,
        'removed_employees': removed_employees,
        'promotions': promotions,
        'transfers': transfers,
        'regular_employees': regular_employees,
        'reporting_period': {'month': 'JULY', 'year': '2025'}
    }

def generate_template_narratives(regular_employees):
    """Generate template-compliant narratives for regular employees only"""
    for emp_id, emp_data in regular_employees.items():
        for i, change in enumerate(emp_data['changes']):
            change['number'] = i + 1
            change['narration'] = create_narrative(change)
    return regular_employees

def create_narrative(change):
    """Create template-compliant narrative"""
    item = change['item_name']
    prev_val = change['previous_value']
    curr_val = change['current_value']
    change_type = change['change_type']

    try:
        prev_num = float(prev_val.replace(',', '')) if prev_val and prev_val != '0' else 0
        curr_num = float(curr_val.replace(',', '')) if curr_val and curr_val != '0' else 0

        if change_type == 'INCREASED':
            difference = curr_num - prev_num
            return f"{item} increased from {prev_num:.2f} to {curr_num:.2f} in July 2025 (increase of {difference:.2f})"
        elif change_type == 'DECREASED':
            difference = prev_num - curr_num
            return f"{item} decreased from {prev_num:.2f} to {curr_num:.2f} in July 2025 (decrease of {difference:.2f})"
        elif change_type == 'NEW':
            return f"NEW {item} of {curr_num:.2f} is added to Payslip for July 2025."
        elif change_type == 'REMOVED':
            return f"{item} of {prev_num:.2f} was removed from Payslip for July 2025."
        else:
            return f"{item} changed from {prev_num:.2f} to {curr_num:.2f} in July 2025"
    except:
        # Handle non-numeric values
        if change_type == 'NEW':
            return f"NEW {item} of {curr_val} is added to Payslip for July 2025."
        elif change_type == 'REMOVED':
            return f"{item} of {prev_val} was removed from Payslip for July 2025."
        else:
            return f"{item} changed from '{prev_val}' to '{curr_val}' in July 2025"

def create_properly_categorized_report(categorized_data):
    """Create Word document with proper categorization"""
    print("\n📄 CREATING PROPERLY CATEGORIZED TEMPLATE REPORT")
    print("=" * 50)

    # Generate narratives for regular employees only
    regular_employees = generate_template_narratives(categorized_data['regular_employees'])

    # Create document
    doc = Document()

    # Title
    title = doc.add_heading('PAYROLL AUDIT REPORT: JULY 2025', 0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER

    # Report Information and Executive Summary Table
    table = doc.add_table(rows=5, cols=2)
    table.style = 'Table Grid'
    table.alignment = WD_TABLE_ALIGNMENT.CENTER

    # Headers
    table.cell(0, 0).text = 'Report Information'
    table.cell(0, 1).text = 'Executive Summary'

    # Report Information (left column)
    table.cell(1, 0).text = 'Period: July 2025'
    table.cell(2, 0).text = f'Generated at {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'
    table.cell(3, 0).text = 'Generated By: Samuel Asiedu'
    table.cell(4, 0).text = 'Designation: Sr. Audit Officer'

    # Executive Summary (right column)
    total_changes = sum(len(emp['changes']) for emp in regular_employees.values())
    significant_changes = len(regular_employees)

    table.cell(1, 1).text = f'Significant Changes Detected: {significant_changes}'
    table.cell(2, 1).text = f'HIGH Priority Changes: {total_changes}'
    table.cell(3, 1).text = f'MODERATE Priority Changes: 0'
    table.cell(4, 1).text = f'LOW Priority Changes: 0'

    # Add spacing
    doc.add_paragraph()

    # Findings and Observations section - ONLY for regular employees
    if regular_employees:
        doc.add_heading('Finding and Observations', level=1)

        # Sort employees by employee ID
        sorted_employees = sorted(regular_employees.items(), key=lambda x: x[1]['employee_id'])

        for emp_id, emp_data in sorted_employees:
            # Employee header
            emp_header = f"{emp_data['employee_id']}: {emp_data['employee_name']} – {emp_data['changes'][0]['section_name']}"
            doc.add_heading(emp_header, level=2)

            # Add numbered findings for this employee
            for change in emp_data['changes']:
                para = doc.add_paragraph()
                para.add_run(f"{change['number']}. {change['narration']}")
                para.style = 'List Number'

    return doc

def add_proper_appendix_sections(doc, categorized_data):
    """Add properly categorized appendix sections"""

    # NEW EMPLOYEES section
    if categorized_data['new_employees']:
        doc.add_heading('NEW EMPLOYEES', level=1)
        doc.add_paragraph('The following Employees were added to July 2025 Payroll:')

        for new_emp in categorized_data['new_employees']:
            para = doc.add_paragraph()
            emp_text = f"• {new_emp['employee_id']}: {new_emp['employee_name']} - {new_emp['added_items']} personal items added"
            para.add_run(emp_text)
            para.style = 'List Bullet'

    # REMOVED EMPLOYEES section
    if categorized_data['removed_employees']:
        doc.add_heading('REMOVED EMPLOYEES', level=1)
        doc.add_paragraph('The following Employees were removed from July 2025 Payroll:')

        for removed_emp in categorized_data['removed_employees']:
            para = doc.add_paragraph()
            emp_text = f"• {removed_emp['employee_id']}: {removed_emp['employee_name']} - {removed_emp['removed_items']} personal items removed"
            para.add_run(emp_text)
            para.style = 'List Bullet'

    # PROMOTIONS section
    if categorized_data['promotions']:
        doc.add_heading('PROMOTIONS', level=1)
        doc.add_paragraph('The following Employees were promoted in July 2025:')

        for promotion in categorized_data['promotions']:
            para = doc.add_paragraph()
            emp_text = f"• {promotion['employee_id']}: {promotion['employee_name']} - Promoted from {promotion['previous_title']} to {promotion['current_title']}"
            para.add_run(emp_text)
            para.style = 'List Bullet'

    # TRANSFERS section
    if categorized_data['transfers']:
        doc.add_heading('TRANSFERS', level=1)
        doc.add_paragraph('The following Employees were transferred in July 2025:')

        for transfer in categorized_data['transfers']:
            para = doc.add_paragraph()
            emp_text = f"• {transfer['employee_id']}: {transfer['employee_name']} - Transferred from {transfer['previous_department']} to {transfer['current_department']}"
            para.add_run(emp_text)
            para.style = 'List Bullet'

    # Add footer
    doc.add_paragraph()
    footer_para = doc.add_paragraph()
    footer_para.add_run('Page 1 | TEMPLAR PAYROLL AUDITOR | All Rights Reserved © 2025')
    footer_para.alignment = WD_ALIGN_PARAGRAPH.CENTER

    return doc

def main():
    """Generate properly categorized July 2025 report"""
    print("🎯 GENERATING PROPERLY CATEGORIZED JULY 2025 REPORT")
    print("=" * 60)
    print("📋 PROPER CATEGORIZATION RULES:")
    print("   ✅ NEW EMPLOYEES: 4+ items added in PERSONAL DETAILS")
    print("   ✅ REMOVED EMPLOYEES: 4+ items removed in PERSONAL DETAILS")
    print("   ✅ PROMOTIONS: Job title changes")
    print("   ✅ TRANSFERS: Department changes")
    print("   ✅ INDIVIDUAL NARRATIVES: Only for employees NOT in appendix")
    print("=" * 60)

    try:
        # Load and categorize data
        categorized_data = load_and_categorize_july_2025_data()
        if not categorized_data:
            print("❌ Failed to load and categorize July 2025 data")
            return False

        # Create properly categorized Word document
        doc = create_properly_categorized_report(categorized_data)

        # Add proper appendix sections
        doc = add_proper_appendix_sections(doc, categorized_data)

        # Save document
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"Properly_Categorized_July_2025_Report_{timestamp}.docx"
        doc.save(filename)

        print(f"\n🎉 PROPERLY CATEGORIZED REPORT COMPLETE!")
        print(f"📄 Report generated: {filename}")
        print(f"\n📊 Final Categorization Statistics:")
        print(f"   👥 REGULAR EMPLOYEES (with individual narratives): {len(categorized_data['regular_employees'])}")
        print(f"   ➕ NEW EMPLOYEES (4+ personal items added): {len(categorized_data['new_employees'])}")
        print(f"   ➖ REMOVED EMPLOYEES (4+ personal items removed): {len(categorized_data['removed_employees'])}")
        print(f"   📈 PROMOTIONS (job title changes): {len(categorized_data['promotions'])}")
        print(f"   🔄 TRANSFERS (department changes): {len(categorized_data['transfers'])}")

        total_individual_changes = sum(len(emp['changes']) for emp in categorized_data['regular_employees'].values())
        print(f"   📋 Total individual change narratives: {total_individual_changes}")

        print(f"\n✅ PROPER CATEGORIZATION APPLIED:")
        print(f"✅ Employees in appendix categories have NO individual narratives")
        print(f"✅ Only regular employees have individual change narratives")
        print(f"✅ Template compliance maintained")

        return True

    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        print(f"📋 Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    main()
