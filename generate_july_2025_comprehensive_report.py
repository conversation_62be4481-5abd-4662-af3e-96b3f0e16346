#!/usr/bin/env python3
"""
Generate Comprehensive Employee-Based Report for July 2025
Includes all appendix categories:
- Promotion Ministers and Staff
- Transfer Staff
- Removed Employees
- New Employees
"""

import sys
import os
import sqlite3
import json
from datetime import datetime
from docx import Document
from docx.shared import Inches
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.table import WD_TABLE_ALIGNMENT

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    
    return None

def load_july_2025_data():
    """Load payroll data for July 2025 analysis"""
    print("📊 LOADING JULY 2025 PAYROLL DATA")
    print("=" * 50)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return None
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get current session
        cursor.execute('SELECT session_id FROM current_session WHERE id = 1')
        current_session_result = cursor.fetchone()
        
        if not current_session_result:
            print("❌ No current session found")
            return None
        
        current_session_id = current_session_result[0]
        print(f"✅ Using session: {current_session_id}")
        
        # Load comparison results (changes between months)
        cursor.execute('''
            SELECT employee_id, employee_name, section_name, item_label,
                   previous_value, current_value, change_type, priority,
                   numeric_difference, percentage_change
            FROM comparison_results
            WHERE change_type != 'NO_CHANGE'
            ORDER BY employee_name, section_name, item_label
        ''')
        comparison_results = cursor.fetchall()

        # Load current employees (July 2025)
        cursor.execute('''
            SELECT DISTINCT employee_id, employee_name, job_title, department
            FROM employees
            WHERE session_id = ?
            ORDER BY employee_name
        ''', (current_session_id,))
        current_employees = cursor.fetchall()

        # Load previous employees (June 2025) - simulate from existing data
        cursor.execute('''
            SELECT DISTINCT employee_id, employee_name
            FROM comparison_results
            WHERE previous_value IS NOT NULL AND previous_value != ''
            ORDER BY employee_name
        ''')
        previous_employees = cursor.fetchall()

        # Load pre-reporting results for detailed analysis
        cursor.execute('''
            SELECT pr.session_id, pr.change_id, pr.selected_for_report,
                   pr.bulk_category, pr.bulk_size, pr.user_notes,
                   cr.employee_id, cr.employee_name, cr.section_name, cr.item_label,
                   cr.previous_value, cr.current_value, cr.change_type, cr.priority,
                   cr.numeric_difference, cr.percentage_change
            FROM pre_reporting_results pr
            JOIN comparison_results cr ON pr.change_id = cr.id
            WHERE cr.change_type != 'NO_CHANGE'
            ORDER BY cr.employee_name, cr.priority DESC
        ''')
        pre_reporting_results = cursor.fetchall()
        
        conn.close()
        
        print(f"✅ Loaded {len(comparison_results)} comparison results")
        print(f"✅ Loaded {len(current_employees)} current employees")
        print(f"✅ Loaded {len(previous_employees)} previous employees")
        print(f"✅ Loaded {len(pre_reporting_results)} pre-reporting results")
        
        return {
            'comparison_results': comparison_results,
            'current_employees': current_employees,
            'previous_employees': previous_employees,
            'pre_reporting_results': pre_reporting_results,
            'reporting_period': {
                'month': 'JULY',
                'year': '2025'
            }
        }
        
    except Exception as e:
        print(f"❌ Error loading data: {e}")
        import traceback
        print(f"📋 Traceback: {traceback.format_exc()}")
        return None

def detect_promotions(changes):
    """Detect promotions based on salary increases and title changes"""
    print("\n🔍 DETECTING PROMOTIONS")
    print("=" * 30)
    
    promotions = {
        'ministers': [],
        'staff': []
    }
    
    # Group changes by employee
    emp_changes = {}
    for change in changes:
        emp_id = change[0]
        if emp_id not in emp_changes:
            emp_changes[emp_id] = []
        emp_changes[emp_id].append(change)
    
    for emp_id, emp_change_list in emp_changes.items():
        employee_name = emp_change_list[0][1]
        department = emp_change_list[0][2] if len(emp_change_list[0]) > 2 else 'Unknown'
        
        # Check for salary increases (Basic Salary)
        salary_increases = []
        title_changes = []
        
        for change in emp_change_list:
            item_label = change[3]  # Updated to use item_label
            previous_value = change[4] or '0'
            current_value = change[5] or '0'
            change_type = change[6]

            # Detect salary increases
            if 'BASIC' in item_label.upper() and 'SALARY' in item_label.upper():
                try:
                    prev_val = float(previous_value.replace(',', ''))
                    curr_val = float(current_value.replace(',', ''))
                    if curr_val > prev_val:
                        salary_increases.append({
                            'item': item_label,
                            'increase': curr_val - prev_val,
                            'from': prev_val,
                            'to': curr_val
                        })
                except:
                    pass

            # Detect title/designation changes
            if any(keyword in item_label.upper() for keyword in ['DESIGNATION', 'TITLE', 'POSITION', 'RANK']):
                if change_type in ['CHANGED', 'UPDATED', 'MODIFIED']:
                    title_changes.append({
                        'item': item_label,
                        'from': previous_value,
                        'to': current_value
                    })
        
        # Determine promotion type
        is_minister = 'MINISTER' in department.upper() or any('MINISTER' in str(change).upper() for change in emp_change_list)
        
        if salary_increases or title_changes:
            promotion_data = {
                'employee_id': emp_id,
                'employee_name': employee_name,
                'department': department,
                'salary_increases': salary_increases,
                'title_changes': title_changes,
                'total_salary_increase': sum(inc['increase'] for inc in salary_increases)
            }
            
            if is_minister:
                promotions['ministers'].append(promotion_data)
            else:
                promotions['staff'].append(promotion_data)
    
    print(f"✅ Found {len(promotions['ministers'])} minister promotions")
    print(f"✅ Found {len(promotions['staff'])} staff promotions")
    
    return promotions

def detect_transfers(changes):
    """Detect transfers based on department changes"""
    print("\n🔍 DETECTING TRANSFERS")
    print("=" * 30)
    
    transfers = []
    
    # Group changes by employee
    emp_changes = {}
    for change in changes:
        emp_id = change[0]
        if emp_id not in emp_changes:
            emp_changes[emp_id] = []
        emp_changes[emp_id].append(change)
    
    for emp_id, emp_change_list in emp_changes.items():
        employee_name = emp_change_list[0][1]
        
        # Look for department/section changes
        for change in emp_change_list:
            item_label = change[3]  # Updated to use item_label
            previous_value = change[4]
            current_value = change[5]
            change_type = change[6]

            # Detect department changes
            if any(keyword in item_label.upper() for keyword in ['DEPARTMENT', 'SECTION', 'UNIT', 'DIVISION']):
                if previous_value and current_value and previous_value != current_value:
                    transfers.append({
                        'employee_id': emp_id,
                        'employee_name': employee_name,
                        'from_department': previous_value,
                        'to_department': current_value,
                        'change_type': change_type
                    })
    
    print(f"✅ Found {len(transfers)} transfers")
    return transfers

def detect_new_employees(current_employees, previous_employees):
    """Detect new employees (in current but not in previous)"""
    print("\n🔍 DETECTING NEW EMPLOYEES")
    print("=" * 30)
    
    # Get employee IDs from previous month
    previous_emp_ids = {emp[0] for emp in previous_employees}
    
    # Find employees in current month but not in previous
    new_employees = []
    for emp in current_employees:
        emp_id = emp[0]
        if emp_id not in previous_emp_ids:
            new_employees.append({
                'employee_id': emp_id,
                'employee_name': emp[1],
                'designation': emp[2] if len(emp) > 2 else 'Unknown',
                'department': emp[3] if len(emp) > 3 else 'Unknown'
            })
    
    print(f"✅ Found {len(new_employees)} new employees")
    return new_employees

def detect_removed_employees(current_employees, previous_employees):
    """Detect removed employees (in previous but not in current)"""
    print("\n🔍 DETECTING REMOVED EMPLOYEES")
    print("=" * 30)
    
    # Get employee IDs from current month
    current_emp_ids = {emp[0] for emp in current_employees}
    
    # Find employees in previous month but not in current
    removed_employees = []
    for emp in previous_employees:
        emp_id = emp[0]
        if emp_id not in current_emp_ids:
            removed_employees.append({
                'employee_id': emp_id,
                'employee_name': emp[1]
            })
    
    print(f"✅ Found {len(removed_employees)} removed employees")
    return removed_employees

def apply_comprehensive_business_rules(data):
    """Apply comprehensive business rules for July 2025 report"""
    print("\n🧠 APPLYING COMPREHENSIVE BUSINESS RULES")
    print("=" * 50)

    # Convert comparison results to standardized format
    changes = []
    for row in data['comparison_results']:
        change = {
            'employee_id': row[0],
            'employee_name': row[1],
            'section_name': row[2],
            'item_name': row[3],  # This is actually item_label from the database
            'previous_value': row[4] or '0',
            'current_value': row[5] or '0',
            'change_type': row[6],
            'priority': row[7] if len(row) > 7 else 'MODERATE',
            'numeric_difference': row[8] if len(row) > 8 else 0,
            'percentage_change': row[9] if len(row) > 9 else 0
        }
        changes.append(change)

    # Detect all categories
    promotions = detect_promotions(data['comparison_results'])
    transfers = detect_transfers(data['comparison_results'])
    new_employees = detect_new_employees(data['current_employees'], data['previous_employees'])
    removed_employees = detect_removed_employees(data['current_employees'], data['previous_employees'])

    # Group regular changes by employee
    employee_groups = {}
    for change in changes:
        emp_id = change['employee_id']
        if emp_id not in employee_groups:
            employee_groups[emp_id] = {
                'employee_id': emp_id,
                'employee_name': change['employee_name'],
                'department': change['section_name'],
                'changes': []
            }
        employee_groups[emp_id]['changes'].append(change)

    # Generate narration for each change
    reporting_month = f"{data['reporting_period']['month']} {data['reporting_period']['year']}"

    for emp_id, emp_data in employee_groups.items():
        for i, change in enumerate(emp_data['changes']):
            change['narration'] = generate_july_narration(change, reporting_month)
            change['number'] = i + 1

    return {
        'employee_groups': employee_groups,
        'promotions': promotions,
        'transfers': transfers,
        'new_employees': new_employees,
        'removed_employees': removed_employees,
        'reporting_period': data['reporting_period'],
        'total_changes': len(changes),
        'high_priority': len([c for c in changes if c['priority'] == 'HIGH']),
        'moderate_priority': len([c for c in changes if c['priority'] == 'MODERATE']),
        'low_priority': len([c for c in changes if c['priority'] == 'LOW'])
    }

def generate_july_narration(change, reporting_month):
    """Generate narration for July 2025 report"""
    item = change['item_name']
    prev_val = change['previous_value']
    curr_val = change['current_value']
    change_type = change['change_type']

    try:
        prev_num = float(prev_val.replace(',', '')) if prev_val and prev_val != '0' else 0
        curr_num = float(curr_val.replace(',', '')) if curr_val and curr_val != '0' else 0

        if change_type == 'INCREASED':
            difference = curr_num - prev_num
            return f"{item} increased from {prev_num:,.2f} to {curr_num:,.2f} (increase of {difference:,.2f}) in {reporting_month}"
        elif change_type == 'DECREASED':
            difference = prev_num - curr_num
            return f"{item} decreased from {prev_num:,.2f} to {curr_num:,.2f} (decrease of {difference:,.2f}) in {reporting_month}"
        elif change_type == 'NEW':
            return f"New {item} of {curr_num:,.2f} added in {reporting_month}"
        elif change_type == 'REMOVED':
            return f"{item} of {prev_num:,.2f} removed in {reporting_month}"
        else:
            return f"{item} changed from {prev_val} to {curr_val} in {reporting_month}"
    except:
        # Handle non-numeric values
        if change_type == 'CHANGED':
            return f"{item} changed from '{prev_val}' to '{curr_val}' in {reporting_month}"
        elif change_type == 'NEW':
            return f"New {item}: '{curr_val}' added in {reporting_month}"
        elif change_type == 'REMOVED':
            return f"{item}: '{prev_val}' removed in {reporting_month}"
        else:
            return f"{item} updated in {reporting_month}"

def create_comprehensive_word_report(processed_data):
    """Create comprehensive Word document for July 2025"""
    print("\n📄 CREATING COMPREHENSIVE JULY 2025 REPORT")
    print("=" * 50)

    # Create document
    doc = Document()

    # Title
    reporting_period = processed_data['reporting_period']
    month_year = f"{reporting_period['month']} {reporting_period['year']}"
    title = doc.add_heading(f'COMPREHENSIVE PAYROLL AUDIT REPORT: {month_year}', 0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER

    # Executive Summary Table
    table = doc.add_table(rows=6, cols=2)
    table.style = 'Table Grid'
    table.alignment = WD_TABLE_ALIGNMENT.CENTER

    # Headers
    table.cell(0, 0).text = 'Report Information'
    table.cell(0, 1).text = 'Executive Summary'

    # Report Information
    table.cell(1, 0).text = f'Reporting Period: {month_year}'
    table.cell(2, 0).text = f'Generated: {datetime.now().strftime("%B %d, %Y at %I:%M %p")}'
    table.cell(3, 0).text = 'Report Type: Comprehensive Employee-Based Analysis'
    table.cell(4, 0).text = 'Focus: All Appendix Categories'
    table.cell(5, 0).text = 'Compliance: Final Report Specification'

    # Executive Summary
    table.cell(1, 1).text = f'Total Employees with Changes: {len(processed_data["employee_groups"])}'
    table.cell(2, 1).text = f'New Employees: {len(processed_data["new_employees"])}'
    table.cell(3, 1).text = f'Removed Employees: {len(processed_data["removed_employees"])}'
    table.cell(4, 1).text = f'Promotions: {len(processed_data["promotions"]["ministers"]) + len(processed_data["promotions"]["staff"])}'
    table.cell(5, 1).text = f'Transfers: {len(processed_data["transfers"])}'

    # Add spacing
    doc.add_paragraph()

    return doc

def add_findings_section(doc, processed_data):
    """Add individual employee findings section"""
    doc.add_heading('FINDINGS AND OBSERVATIONS', level=1)

    # Sort employees by name for consistent reporting
    sorted_employees = sorted(processed_data['employee_groups'].items(),
                            key=lambda x: x[1]['employee_name'])

    for emp_id, emp_data in sorted_employees:
        # Employee header (without department as per specification)
        emp_header = f"{emp_data['employee_id']}: {emp_data['employee_name']}"
        doc.add_heading(emp_header, level=2)

        # Add findings for this employee
        for change in emp_data['changes']:
            para = doc.add_paragraph()
            para.add_run(f"{change['number']}. {change['narration']}")
            para.style = 'List Number'

    return doc

def add_appendix_sections(doc, processed_data):
    """Add all appendix sections"""

    # APPENDIX A: PROMOTION MINISTERS
    if processed_data['promotions']['ministers']:
        doc.add_heading('APPENDIX A: PROMOTION MINISTERS', level=1)
        doc.add_paragraph("The following ministers received promotions in July 2025:")

        for i, promo in enumerate(processed_data['promotions']['ministers'], 1):
            para = doc.add_paragraph()
            promotion_text = f"{i}. {promo['employee_id']}: {promo['employee_name']} - {promo['department']}"

            if promo['salary_increases']:
                total_increase = sum(inc['increase'] for inc in promo['salary_increases'])
                promotion_text += f" (Salary increase: {total_increase:,.2f})"

            if promo['title_changes']:
                for title_change in promo['title_changes']:
                    promotion_text += f" (Title changed from '{title_change['from']}' to '{title_change['to']}')"

            para.add_run(promotion_text)
            para.style = 'List Number'

    # APPENDIX B: PROMOTION STAFF
    if processed_data['promotions']['staff']:
        doc.add_heading('APPENDIX B: PROMOTION STAFF', level=1)
        doc.add_paragraph("The following staff members received promotions in July 2025:")

        for i, promo in enumerate(processed_data['promotions']['staff'], 1):
            para = doc.add_paragraph()
            promotion_text = f"{i}. {promo['employee_id']}: {promo['employee_name']} - {promo['department']}"

            if promo['salary_increases']:
                total_increase = sum(inc['increase'] for inc in promo['salary_increases'])
                promotion_text += f" (Salary increase: {total_increase:,.2f})"

            if promo['title_changes']:
                for title_change in promo['title_changes']:
                    promotion_text += f" (Title changed from '{title_change['from']}' to '{title_change['to']}')"

            para.add_run(promotion_text)
            para.style = 'List Number'

    # APPENDIX C: TRANSFER STAFF
    if processed_data['transfers']:
        doc.add_heading('APPENDIX C: TRANSFER STAFF', level=1)
        doc.add_paragraph("The following staff members were transferred in July 2025:")

        for i, transfer in enumerate(processed_data['transfers'], 1):
            para = doc.add_paragraph()
            transfer_text = f"{i}. {transfer['employee_id']}: {transfer['employee_name']} - Transferred from '{transfer['from_department']}' to '{transfer['to_department']}'"
            para.add_run(transfer_text)
            para.style = 'List Number'

    # APPENDIX D: NEW EMPLOYEES
    if processed_data['new_employees']:
        doc.add_heading('APPENDIX D: NEW EMPLOYEES', level=1)
        doc.add_paragraph("The following employees were newly added in July 2025:")

        for i, new_emp in enumerate(processed_data['new_employees'], 1):
            para = doc.add_paragraph()
            new_emp_text = f"{i}. {new_emp['employee_id']}: {new_emp['employee_name']} - {new_emp['designation']} ({new_emp['department']})"
            para.add_run(new_emp_text)
            para.style = 'List Number'

    # APPENDIX E: REMOVED EMPLOYEES
    if processed_data['removed_employees']:
        doc.add_heading('APPENDIX E: REMOVED EMPLOYEES', level=1)
        doc.add_paragraph("The following employees were removed in July 2025:")

        for i, removed_emp in enumerate(processed_data['removed_employees'], 1):
            para = doc.add_paragraph()
            removed_emp_text = f"{i}. {removed_emp['employee_id']}: {removed_emp['employee_name']}"
            para.add_run(removed_emp_text)
            para.style = 'List Number'

    return doc

def add_summary_statistics(doc, processed_data):
    """Add summary statistics section"""
    doc.add_heading('SUMMARY STATISTICS', level=1)

    # Create statistics table
    stats_table = doc.add_table(rows=8, cols=2)
    stats_table.style = 'Table Grid'

    stats_table.cell(0, 0).text = 'Category'
    stats_table.cell(0, 1).text = 'Count'

    stats_table.cell(1, 0).text = 'Total Employees with Changes'
    stats_table.cell(1, 1).text = str(len(processed_data['employee_groups']))

    stats_table.cell(2, 0).text = 'Minister Promotions'
    stats_table.cell(2, 1).text = str(len(processed_data['promotions']['ministers']))

    stats_table.cell(3, 0).text = 'Staff Promotions'
    stats_table.cell(3, 1).text = str(len(processed_data['promotions']['staff']))

    stats_table.cell(4, 0).text = 'Transfers'
    stats_table.cell(4, 1).text = str(len(processed_data['transfers']))

    stats_table.cell(5, 0).text = 'New Employees'
    stats_table.cell(5, 1).text = str(len(processed_data['new_employees']))

    stats_table.cell(6, 0).text = 'Removed Employees'
    stats_table.cell(6, 1).text = str(len(processed_data['removed_employees']))

    stats_table.cell(7, 0).text = 'Total Changes Analyzed'
    stats_table.cell(7, 1).text = str(processed_data['total_changes'])

    return doc

def main():
    """Generate comprehensive July 2025 employee-based report"""
    print("🎯 GENERATING COMPREHENSIVE JULY 2025 EMPLOYEE REPORT")
    print("=" * 60)
    print("📋 Report Features:")
    print("   ✅ All Appendix Categories")
    print("   ✅ Promotion Ministers and Staff")
    print("   ✅ Transfer Staff")
    print("   ✅ New Employees")
    print("   ✅ Removed Employees")
    print("   ✅ Individual Employee Findings")
    print("   ✅ Summary Statistics")
    print("=" * 60)

    try:
        # Load July 2025 data
        data = load_july_2025_data()
        if not data:
            print("❌ Failed to load July 2025 data")
            return False

        # Apply comprehensive business rules
        processed_data = apply_comprehensive_business_rules(data)

        # Create Word document
        doc = create_comprehensive_word_report(processed_data)

        # Add findings section
        doc = add_findings_section(doc, processed_data)

        # Add all appendix sections
        doc = add_appendix_sections(doc, processed_data)

        # Add summary statistics
        doc = add_summary_statistics(doc, processed_data)

        # Save document
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"Comprehensive_July_2025_Employee_Report_{timestamp}.docx"
        doc.save(filename)

        print(f"\n🎉 SUCCESS!")
        print(f"📄 Comprehensive July 2025 report generated: {filename}")
        print(f"\n📊 Report Contents:")
        print(f"   📋 Individual Findings: {len(processed_data['employee_groups'])} employees")
        print(f"   👑 Minister Promotions: {len(processed_data['promotions']['ministers'])}")
        print(f"   👥 Staff Promotions: {len(processed_data['promotions']['staff'])}")
        print(f"   🔄 Transfers: {len(processed_data['transfers'])}")
        print(f"   ➕ New Employees: {len(processed_data['new_employees'])}")
        print(f"   ➖ Removed Employees: {len(processed_data['removed_employees'])}")
        print(f"   📈 Total Changes: {processed_data['total_changes']}")
        print(f"\n✅ This is the complete July 2025 comprehensive employee report!")

        return True

    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        print(f"📋 Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    main()
