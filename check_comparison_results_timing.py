#!/usr/bin/env python3
"""
Check for comparison results created between 1:50am and 1:53am
"""

import sqlite3
import os
from datetime import datetime

def check_comparison_results_timing():
    """Check for comparison results in the specified time range"""
    
    db_path = r"data\templar_payroll_auditor.db"
    if not os.path.exists(db_path):
        print(f"❌ Database not found at: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check for records between 1:50am and 1:53am today
        print("🔍 CHECKING COMPARISON RESULTS TIMING")
        print("=" * 50)
        
        # Specific time range 1:50-1:53am
        cursor.execute('''
            SELECT COUNT(*) 
            FROM comparison_results 
            WHERE created_at >= '2025-06-30 01:50:00' 
            AND created_at <= '2025-06-30 01:53:59'
        ''')
        count_1_50_53 = cursor.fetchone()[0]
        
        # Broader range 1:50-1:59am
        cursor.execute('''
            SELECT COUNT(*) 
            FROM comparison_results 
            WHERE created_at LIKE '2025-06-30 01:5%'
        ''')
        count_1_50_59 = cursor.fetchone()[0]
        
        # All records from 1:00-1:59am
        cursor.execute('''
            SELECT COUNT(*) 
            FROM comparison_results 
            WHERE created_at LIKE '2025-06-30 01:%'
        ''')
        count_all_1am = cursor.fetchone()[0]
        
        print(f"📊 Results for 2025-06-30:")
        print(f"   • 1:50:00 - 1:53:59 AM: {count_1_50_53} records")
        print(f"   • 1:50:xx - 1:59:xx AM: {count_1_50_59} records")
        print(f"   • All 1:xx AM records: {count_all_1am} records")
        
        # If we have records in the target range, show details
        if count_1_50_53 > 0:
            print(f"\n📋 DETAILED RECORDS (1:50-1:53 AM):")
            print("=" * 50)
            
            cursor.execute('''
                SELECT id, session_id, employee_id, employee_name, section_name, item_label,
                       previous_value, current_value, change_type, priority, created_at
                FROM comparison_results 
                WHERE created_at >= '2025-06-30 01:50:00' 
                AND created_at <= '2025-06-30 01:53:59'
                ORDER BY created_at DESC
                LIMIT 10
            ''')
            results = cursor.fetchall()
            
            for i, result in enumerate(results, 1):
                print(f"\n{i}. Record ID: {result[0]}")
                print(f"   Session: {result[1]}")
                print(f"   Employee: {result[2]} - {result[3]}")
                print(f"   Section: {result[4]}")
                print(f"   Item: {result[5]}")
                print(f"   Change: {result[6]} → {result[7]} ({result[8]})")
                print(f"   Priority: {result[9]}")
                print(f"   Created: {result[10]}")
        
        # Show recent records for context
        print(f"\n📋 MOST RECENT COMPARISON RESULTS:")
        print("=" * 50)
        
        cursor.execute('''
            SELECT id, session_id, employee_id, employee_name, created_at
            FROM comparison_results 
            ORDER BY created_at DESC
            LIMIT 5
        ''')
        recent_results = cursor.fetchall()
        
        for i, result in enumerate(recent_results, 1):
            print(f"{i}. ID: {result[0]} | Session: {result[1]} | Employee: {result[2]} - {result[3]} | Created: {result[4]}")
        
        # Check current session
        cursor.execute('SELECT session_id FROM current_session WHERE id = 1')
        current_session = cursor.fetchone()
        if current_session:
            print(f"\n🎯 Current Session: {current_session[0]}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        print(f"📋 Traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    check_comparison_results_timing()
