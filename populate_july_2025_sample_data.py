#!/usr/bin/env python3
"""
Populate July 2025 Sample Data
Creates realistic sample payroll data to demonstrate the comprehensive report functionality
"""

import sqlite3
import random
from datetime import datetime

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        import os
        if os.path.exists(path):
            return path
    
    return None

def create_sample_employees():
    """Create sample employee data"""
    employees = [
        # Current employees (July 2025)
        ("COP2626", "SAMUEL ASIEDU", "AUDIT, MONITORING & EVALUATION", "Senior Auditor"),
        ("COP2524", "MARK ANTHONY", "FINANCE", "Finance Officer"),
        ("COP2789", "MARY JOHNSON", "HUMAN RESOURCES", "HR Manager"),
        ("COP2890", "<PERSON><PERSON><PERSON> SMITH", "OPERATIONS", "Operations Manager"),
        ("COP2991", "SARAH WILLIAMS", "LEGAL", "Legal Counsel"),
        ("COP3001", "DAVID BROWN", "IT", "IT Specialist"),
        ("COP3102", "LISA DAVIS", "PROCUREMENT", "Procurement Officer"),
        ("COP3203", "MICHAEL WILSON", "SECURITY", "Security Chief"),
        ("COP3304", "JENNIFER TAYLOR", "COMMUNICATIONS", "Communications Officer"),
        ("COP3405", "ROBERT ANDERSON", "PLANNING", "Planning Officer"),
        # New employees (added in July 2025)
        ("COP3506", "GRACE THOMPSON", "FINANCE", "Junior Accountant"),
        ("COP3607", "PETER MARTINEZ", "HR", "HR Assistant"),
        ("COP3708", "ANGELA GARCIA", "OPERATIONS", "Operations Assistant"),
    ]
    
    return employees

def create_sample_changes():
    """Create sample payroll changes for July 2025"""
    changes = [
        # Promotions (salary increases + title changes)
        ("COP2626", "SAMUEL ASIEDU", "EARNINGS", "BASIC SALARY", "8500.00", "12000.00", "INCREASED", "HIGH"),
        ("COP2789", "MARY JOHNSON", "EARNINGS", "BASIC SALARY", "9000.00", "13500.00", "INCREASED", "HIGH"),
        ("COP2890", "JOHN SMITH", "PERSONAL DETAILS", "DESIGNATION", "Operations Officer", "Operations Manager", "CHANGED", "MODERATE"),
        
        # Transfers (department changes)
        ("COP2991", "SARAH WILLIAMS", "PERSONAL DETAILS", "DEPARTMENT", "OPERATIONS", "LEGAL", "CHANGED", "MODERATE"),
        ("COP3001", "DAVID BROWN", "PERSONAL DETAILS", "SECTION", "GENERAL IT", "CYBERSECURITY", "CHANGED", "MODERATE"),
        
        # Regular changes (allowances, deductions, etc.)
        ("COP2524", "MARK ANTHONY", "ALLOWANCES", "TRANSPORT ALLOWANCE", "500.00", "600.00", "INCREASED", "LOW"),
        ("COP3102", "LISA DAVIS", "DEDUCTIONS", "INCOME TAX", "1200.00", "1350.00", "INCREASED", "MODERATE"),
        ("COP3203", "MICHAEL WILSON", "ALLOWANCES", "SECURITY ALLOWANCE", "800.00", "1000.00", "INCREASED", "LOW"),
        ("COP3304", "JENNIFER TAYLOR", "EARNINGS", "OVERTIME", "0.00", "450.00", "NEW", "LOW"),
        ("COP3405", "ROBERT ANDERSON", "DEDUCTIONS", "LOAN REPAYMENT", "300.00", "0.00", "REMOVED", "LOW"),
        
        # Minister-level changes (for demonstration)
        ("COP2789", "MARY JOHNSON", "PERSONAL DETAILS", "DEPARTMENT", "HUMAN RESOURCES - MINISTER", "HUMAN RESOURCES - MINISTER", "NO_CHANGE", "LOW"),
        ("COP2890", "JOHN SMITH", "PERSONAL DETAILS", "DEPARTMENT", "OPERATIONS - MINISTER", "OPERATIONS - MINISTER", "NO_CHANGE", "LOW"),
    ]
    
    return changes

def populate_sample_data():
    """Populate the database with sample July 2025 data"""
    print("📊 POPULATING JULY 2025 SAMPLE DATA")
    print("=" * 50)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database file not found")
            return False
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get or create current session
        session_id = "july_2025_sample_session"
        
        # Clear existing sample data
        cursor.execute('DELETE FROM employees WHERE session_id = ?', (session_id,))
        cursor.execute('DELETE FROM comparison_results WHERE session_id = ?', (session_id,))
        
        # Update current session
        cursor.execute('DELETE FROM current_session')
        cursor.execute('INSERT INTO current_session (id, session_id) VALUES (1, ?)', (session_id,))
        
        # Insert sample employees
        employees = create_sample_employees()
        for emp in employees:
            cursor.execute('''
                INSERT INTO employees (session_id, employee_id, employee_name, department, job_title, created_at)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (session_id, emp[0], emp[1], emp[2], emp[3], datetime.now()))
        
        print(f"✅ Inserted {len(employees)} sample employees")
        
        # Insert sample changes
        changes = create_sample_changes()
        for i, change in enumerate(changes):
            # Calculate numeric difference and percentage change
            try:
                prev_val = float(change[4].replace(',', '')) if change[4] and change[4] != '0.00' else 0
                curr_val = float(change[5].replace(',', '')) if change[5] and change[5] != '0.00' else 0
                numeric_diff = curr_val - prev_val
                percentage_change = (numeric_diff / prev_val * 100) if prev_val > 0 else 0
            except:
                numeric_diff = 0
                percentage_change = 0
            
            cursor.execute('''
                INSERT INTO comparison_results 
                (session_id, employee_id, employee_name, section_name, item_label, 
                 previous_value, current_value, change_type, priority, 
                 numeric_difference, percentage_change, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (session_id, change[0], change[1], change[2], change[3], 
                  change[4], change[5], change[6], change[7], 
                  numeric_diff, percentage_change, datetime.now()))
            
            # Add to pre_reporting_results
            cursor.execute('''
                INSERT INTO pre_reporting_results
                (session_id, change_id, selected_for_report, bulk_category, bulk_size, created_at)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (session_id, i + 1, True, 'Individual', 1, datetime.now()))
        
        print(f"✅ Inserted {len(changes)} sample changes")
        
        # Create some "previous month" employees (simulate June 2025)
        previous_employees = [
            ("COP2626", "SAMUEL ASIEDU"),
            ("COP2524", "MARK ANTHONY"),
            ("COP2789", "MARY JOHNSON"),
            ("COP2890", "JOHN SMITH"),
            ("COP2991", "SARAH WILLIAMS"),
            ("COP3001", "DAVID BROWN"),
            ("COP3102", "LISA DAVIS"),
            ("COP3203", "MICHAEL WILSON"),
            ("COP3304", "JENNIFER TAYLOR"),
            ("COP3405", "ROBERT ANDERSON"),
            # Removed employee (was in June, not in July)
            ("COP2999", "REMOVED EMPLOYEE"),
        ]
        
        # Add some comparison results for removed employees
        cursor.execute('''
            INSERT INTO comparison_results 
            (session_id, employee_id, employee_name, section_name, item_label, 
             previous_value, current_value, change_type, priority, 
             numeric_difference, percentage_change, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (session_id, "COP2999", "REMOVED EMPLOYEE", "PERSONAL DETAILS", "STATUS", 
              "ACTIVE", "", "REMOVED", "HIGH", 0, 0, datetime.now()))
        
        conn.commit()
        conn.close()
        
        print(f"✅ Sample July 2025 data populated successfully!")
        print(f"📋 Data includes:")
        print(f"   • {len(employees)} employees (including 3 new)")
        print(f"   • {len(changes)} payroll changes")
        print(f"   • Promotions, transfers, new/removed employees")
        print(f"   • Minister and staff categories")
        
        return True
        
    except Exception as e:
        print(f"❌ Error populating data: {e}")
        import traceback
        print(f"📋 Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    if populate_sample_data():
        print("\n🎯 Now run: python generate_july_2025_comprehensive_report.py")
    else:
        print("\n❌ Failed to populate sample data")
